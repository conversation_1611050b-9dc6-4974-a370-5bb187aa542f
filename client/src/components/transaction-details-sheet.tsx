"use client"

import {
  ArrowDownIcon,
  ArrowUpIcon,
  CreditCardIcon,
  DollarSignIcon,
  PencilIcon,
  TrendingDownIcon,
  TrendingUpIcon,
  CalendarIcon,
  TagIcon,
  FileTextIcon,
  ClipboardIcon,
} from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet"
import { Separator } from "@/components/ui/separator"
import { toast } from "@/components/ui/use-toast"

const typeIcons = {
  income: <ArrowUpIcon className="h-5 w-5 text-emerald-500" />,
  expense: <ArrowDownIcon className="h-5 w-5 text-rose-500" />,
  debt_bought: <TrendingDownIcon className="h-5 w-5 text-amber-500" />,
  debt_given: <TrendingUpIcon className="h-5 w-5 text-amber-500" />,
  investment: <DollarSignIcon className="h-5 w-5 text-blue-500" />,
  tax_paid: <CreditCardIcon className="h-5 w-5 text-purple-500" />,
}

const typeColors = {
  income: "bg-emerald-100 text-emerald-800",
  expense: "bg-rose-100 text-rose-800",
  debt_bought: "bg-amber-100 text-amber-800",
  debt_given: "bg-amber-100 text-amber-800",
  investment: "bg-blue-100 text-blue-800",
  tax_paid: "bg-purple-100 text-purple-800",
}

const categoryIcons = {
  Income: "💰",
  Housing: "🏠",
  Food: "🍔",
  Transportation: "🚗",
  Entertainment: "🎬",
  Utilities: "💡",
  Debt: "💳",
  Investment: "📈",
  Tax: "📝",
  Other: "📦",
}

interface TransactionDetailsSheetProps {
  transaction: {
    id: string
    type: string
    name: string
    category: string
    amount: number
    date: string
    note: string
  }
  open: boolean
  onOpenChange: (open: boolean) => void
  onEdit: () => void
}

export function TransactionDetailsSheet({ transaction, open, onOpenChange, onEdit }: TransactionDetailsSheetProps) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "Transaction ID has been copied to clipboard",
      duration: 2000,
    })
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[500px] w-full overflow-y-auto">
        <SheetHeader className="space-y-1">
          <SheetTitle className="text-2xl">Transaction Details</SheetTitle>
          <SheetDescription>View complete information about this transaction</SheetDescription>
        </SheetHeader>

        <div className="py-6 space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {typeIcons[transaction.type]}
              <Badge variant="outline" className={`${typeColors[transaction.type]} text-sm px-3 py-1`}>
                {transaction.type.replace("_", " ")}
              </Badge>
            </div>
            <div
              className={`text-2xl font-bold ${
                transaction.type === "income"
                  ? "text-emerald-600"
                  : transaction.type === "expense"
                    ? "text-rose-600"
                    : ""
              }`}
            >
              {transaction.type === "income" ? "+" : transaction.type === "expense" ? "-" : ""}$
              {transaction.amount.toFixed(2)}
            </div>
          </div>

          <Separator />

          <div className="space-y-5">
            <div className="flex items-start gap-3">
              <div className="bg-muted/60 p-2 rounded-md">
                <FileTextIcon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Name</p>
                <p className="text-lg font-medium">{transaction.name}</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="bg-muted/60 p-2 rounded-md">
                <TagIcon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Category</p>
                <div className="flex items-center gap-2">
                  <span className="text-lg">{categoryIcons[transaction.category]}</span>
                  <p className="text-lg font-medium">{transaction.category}</p>
                </div>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="bg-muted/60 p-2 rounded-md">
                <CalendarIcon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-1">
                <p className="text-sm font-medium text-muted-foreground">Date</p>
                <p className="text-lg font-medium">
                  {new Date(transaction.date).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>
            </div>

            {transaction.note && (
              <div className="flex items-start gap-3">
                <div className="bg-muted/60 p-2 rounded-md">
                  <FileTextIcon className="h-5 w-5 text-muted-foreground" />
                </div>
                <div className="space-y-1">
                  <p className="text-sm font-medium text-muted-foreground">Note</p>
                  <p className="text-lg">{transaction.note}</p>
                </div>
              </div>
            )}

            <div className="flex items-start gap-3">
              <div className="bg-muted/60 p-2 rounded-md">
                <ClipboardIcon className="h-5 w-5 text-muted-foreground" />
              </div>
              <div className="space-y-1 flex-1">
                <p className="text-sm font-medium text-muted-foreground">Transaction ID</p>
                <div className="flex items-center justify-between">
                  <p className="text-sm font-mono">{transaction.id}</p>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(transaction.id)}
                    className="h-8 px-2"
                  >
                    Copy
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <SheetFooter className="flex flex-col sm:flex-row gap-3">
          <SheetClose asChild>
            <Button variant="outline" className="flex-1">
              Close
            </Button>
          </SheetClose>
          <Button onClick={onEdit} className="flex-1">
            <PencilIcon className="mr-2 h-4 w-4" />
            Edit Transaction
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}
