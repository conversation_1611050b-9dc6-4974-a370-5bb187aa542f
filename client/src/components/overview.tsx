"use client";

import { useEffect, useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";

// Fallback data in case API fails
const fallbackData = [
  {
    name: "<PERSON>",
    income: 2400,
    expenses: 1400,
  },
  {
    name: "Feb",
    income: 1398,
    expenses: 980,
  },
  {
    name: "Mar",
    income: 9800,
    expenses: 2290,
  },
];

interface OverviewProps {
  monthlyData?: Array<{
    name: string;
    income: number;
    expenses: number;
  }>;
  isLoading?: boolean;
}

export function Overview({ monthlyData, isLoading = false }: OverviewProps) {
  const [chartData, setChartData] = useState(fallbackData);
  const [loading, setLoading] = useState(isLoading);
  const [error, setError] = useState(false);

  // If data is provided via props, use it
  useEffect(() => {
    if (monthlyData && monthlyData.length > 0) {
      setChartData(monthlyData);
      setLoading(false);
      setError(false);
    } else if (monthlyData && monthlyData.length === 0) {
      setChartData(fallbackData);
      setLoading(false);
      setError(false);
    }
  }, [monthlyData]);

  if (loading) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <Skeleton className="h-[350px] w-full rounded-lg" />
        <div className="flex justify-center gap-6 mt-4">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-[350px] flex items-center justify-center border rounded-lg">
        <p className="text-muted-foreground">
          Failed to load chart data. Please try again later.
        </p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={350}>
        <BarChart
          data={chartData}
          margin={{ top: 10, right: 10, left: 0, bottom: 10 }}
        >
          <CartesianGrid
            strokeDasharray="3 3"
            vertical={false}
            stroke="#f0f0f0"
          />
          <XAxis
            dataKey="name"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            padding={{ left: 10, right: 10 }}
          />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `${formatCurrency(value)}`}
            width={60}
          />
          <Tooltip
            formatter={(value: number) => [`${formatCurrency(value)}`, ""]}
            labelFormatter={(label) => `Month: ${label}`}
            contentStyle={{
              borderRadius: "8px",
              boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
              border: "1px solid #e2e8f0",
              padding: "8px 12px",
            }}
            cursor={{ fill: "rgba(0, 0, 0, 0.04)" }}
          />
          <Bar
            dataKey="income"
            fill="hsl(142.1, 76.2%, 36.3%)"
            radius={[4, 4, 0, 0]}
            name="Income"
            barSize={24}
            animationDuration={1000}
          />
          <Bar
            dataKey="expenses"
            fill="hsl(346.8, 77.2%, 49.8%)"
            radius={[4, 4, 0, 0]}
            name="Expenses"
            barSize={24}
            animationDuration={1000}
          />
        </BarChart>
      </ResponsiveContainer>
      <div className="flex justify-center gap-6 mt-2">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-[hsl(142.1,76.2%,36.3%)]"></div>
          <span className="text-sm font-medium">Income</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-[hsl(346.8,77.2%,49.8%)]"></div>
          <span className="text-sm font-medium">Expenses</span>
        </div>
      </div>
    </div>
  );
}
