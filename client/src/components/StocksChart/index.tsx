import { formatCurrency, formatDate } from "@/lib/utils";
import { StockDataType } from "@/services/investment.service";
import React, { useRef } from "react";
import {
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer,
  Area,
  AreaChart,
} from "recharts";

interface FolioChartProps {
  folio: StockDataType[];
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    name: string;
    dataKey: string;
    payload: Record<string, unknown>;
  }>;
  label?: string;
}

// Custom tooltip component for Recharts
const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
  if (!active || !payload || payload.length === 0) return null;

  return (
    <div className="bg-gray-800/90 p-2 rounded text-white text-xs font-sans">
      <p className="m-0 font-bold text-xs">{label}</p>
      {payload.map((entry, index) => (
        <p key={`item-${index}`} className="mt-1 mb-0">
          {entry.name}: {formatCurrency(entry.value)}
        </p>
      ))}
    </div>
  );
};

const StocksChart = ({ folio = [] }: FolioChartProps) => {
  const chartRef = useRef<HTMLDivElement>(null);

  if (folio.length === 0) return null;

  const chartData = folio[0].data.map(([timestamp, value], index) => {
    const dataPoint: Record<string, string | number> = {
      timestamp,
      date: formatDate(timestamp),
      [folio[0].name]: value,
    };

    for (let i = 1; i < folio.length; i++) {
      const seriesData = folio[i].data;
      if (index < seriesData.length) {
        dataPoint[folio[i].name] = seriesData[index][1];
      }
    }

    return dataPoint;
  });

  // Get the latest value for display
  const getLatestValue = () => {
    if (folio.length > 0 && folio[0].data.length > 0) {
      const latestData = folio[0].data[folio[0].data.length - 1];
      return latestData[1];
    }
    return 0;
  };

  const latestValue = getLatestValue();

  const calculateChange = () => {
    if (folio.length > 0 && folio[0].data.length > 1) {
      const currentValue = folio[0].data[folio[0].data.length - 1][1];
      const previousValue = folio[0].data[folio[0].data.length - 2][1];
      return {
        amount: currentValue - previousValue,
        percent: ((currentValue - previousValue) / previousValue) * 100,
      };
    }
    return { amount: 0, percent: 0 };
  };

  const change = calculateChange();
  const isPositive = change.amount >= 0;

  const formattedPrice = `₹${latestValue.toLocaleString("en-IN")}`;

  const formattedChange = `${isPositive ? "+" : ""}${change.amount.toFixed(
    2
  )} (${isPositive ? "+" : ""}${change.percent.toFixed(2)}%)`;

  return (
    <div className="bg-white rounded-xl shadow-sm p-4 border border-gray-100">
      <div className="flex justify-between items-start mb-3">
        <div>
          <h3 className="text-base font-semibold m-0 text-gray-800">
            {folio[0].name}
          </h3>
          <p className="text-xs text-gray-500 mt-1 mb-0">Stock Performance</p>
        </div>
        <div>
          <div className="text-xl font-semibold text-gray-800 text-right">
            {formattedPrice}
          </div>
          <div
            className={`text-sm font-medium text-right mt-0.5 ${
              isPositive ? "text-green-500" : "text-red-500"
            }`}
          >
            {formattedChange}
          </div>
        </div>
      </div>

      <div className="h-[160px] w-full" ref={chartRef}>
        <ResponsiveContainer width="99%" height="99%" minWidth={100}>
          <AreaChart
            data={chartData}
            margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
          >
            <defs>
              <linearGradient
                id={`colorGradient-${folio[0].name}`}
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="5%" stopColor="#36A2EB" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#36A2EB" stopOpacity={0} />
              </linearGradient>
            </defs>
            <CartesianGrid
              strokeDasharray="3 3"
              vertical={false}
              stroke="rgba(0, 0, 0, 0.05)"
              horizontal={true}
              horizontalPoints={[]}
            />
            <XAxis
              dataKey="date"
              tick={{
                fontSize: 9,
                fontFamily: "'Poppins', 'Segoe UI', sans-serif",
                fill: "#6c757d",
              }}
              tickMargin={4}
              axisLine={false}
              tickLine={false}
              minTickGap={25}
              interval="preserveStartEnd"
              padding={{ left: 0, right: 0 }}
              allowDataOverflow={false}
            />
            <YAxis
              tick={{
                fontSize: 10,
                fontFamily: "'Poppins', 'Segoe UI', sans-serif",
                fill: "#6c757d",
              }}
              tickFormatter={(value) => value.toLocaleString("en-IN")}
              tickMargin={4}
              axisLine={false}
              tickLine={false}
              domain={["auto", "auto"]}
            />
            <RechartsTooltip content={<CustomTooltip />} />
            <Legend wrapperStyle={{ display: "none" }} />

            <Area
              type="monotone"
              dataKey={folio[0].name}
              stroke="#36A2EB"
              strokeWidth={2}
              fillOpacity={0.2}
              fill={"#36A2EB"}
              dot={{
                r: 2,
                fill: "#36A2EB",
                strokeWidth: 1,
                // stroke: "#ffffff",
              }}
              activeDot={{
                r: 5,
                fill: "#36A2EB",
                // stroke: "#ffffff",
                strokeWidth: 2,
              }}
              isAnimationActive={false}
              connectNulls={true}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default StocksChart;
