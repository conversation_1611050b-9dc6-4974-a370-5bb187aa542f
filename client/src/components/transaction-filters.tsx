"use client";

import type React from "react";
import { useState } from "react";
import {
  CalendarIcon,
  SearchIcon,
  ChevronDownIcon,
  ArrowDownIcon,
  ArrowUpIcon,
  FilterIcon,
  XIcon,
} from "lucide-react";
import { format } from "date-fns";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { motion } from "framer-motion";
import { EXPENSE_TYPE, EXPENSE_MAP } from "@/services/expense.service";

// Available categories
const categories = [
  "Income",
  "Housing",
  "Food",
  "Transportation",
  "Entertainment",
  "Utilities",
  "Debt",
  "Investment",
  "Tax",
  "Other",
];

interface TransactionFiltersProps {
  filters: {
    type: string;
    categories: string[];
    dateRange: {
      from: Date | undefined;
      to: Date | undefined;
    };
    searchQuery: string;
    sortBy: string;
    sortDirection: "asc" | "desc";
    ignoreDate?: boolean;
  };
  setFilters: React.Dispatch<
    React.SetStateAction<{
      type: string;
      categories: string[];
      dateRange: {
        from: Date | undefined;
        to: Date | undefined;
      };
      searchQuery: string;
      sortBy: string;
      sortDirection: "asc" | "desc";
      ignoreDate?: boolean;
    }>
  >;
  hideDateFilter?: boolean;
}

export function TransactionFilters({
  filters,
  setFilters,
  hideDateFilter = false,
}: TransactionFiltersProps) {
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);

  const handleReset = () => {
    setFilters({
      type: "all",
      categories: [],
      dateRange: {
        from: undefined,
        to: undefined,
      },
      searchQuery: "",
      sortBy: "date",
      sortDirection: "desc",
      ignoreDate: filters.ignoreDate, // Preserve the ignoreDate setting
    });
  };

  const handleCategoryToggle = (category: string) => {
    setFilters((prev) => {
      if (prev.categories.includes(category)) {
        return {
          ...prev,
          categories: prev.categories.filter((c) => c !== category),
        };
      } else {
        return {
          ...prev,
          categories: [...prev.categories, category],
        };
      }
    });
  };

  const handleSortChange = (value: string) => {
    const [sortBy, sortDirection] = value.split("-");
    setFilters((prev) => ({
      ...prev,
      sortBy,
      sortDirection: sortDirection as "asc" | "desc",
    }));
  };

  const getSortLabel = () => {
    switch (`${filters.sortBy}-${filters.sortDirection}`) {
      case "date-desc":
        return "Date (Newest First)";
      case "date-asc":
        return "Date (Oldest First)";
      case "amount-desc":
        return "Amount (High to Low)";
      case "amount-asc":
        return "Amount (Low to High)";
      case "name-asc":
        return "Name (A-Z)";
      case "name-desc":
        return "Name (Z-A)";
      default:
        return "Sort by";
    }
  };

  const hasActiveFilters =
    filters.type !== "all" ||
    filters.categories.length > 0 ||
    (!hideDateFilter && (filters.dateRange.from || filters.dateRange.to)) ||
    filters.searchQuery;

  return (
    <div className="space-y-4">
      <div className="relative">
        <SearchIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="Search transactions..."
          className="pl-10 pr-4 h-11 rounded-lg border-muted-foreground/20"
          value={filters.searchQuery}
          onChange={(e) =>
            setFilters({ ...filters, searchQuery: e.target.value })
          }
        />
      </div>

      <div className="md:hidden flex items-center justify-between gap-2">
        <Sheet open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2 h-10">
              <FilterIcon className="h-4 w-4" />
              <span>Filters</span>
              {hasActiveFilters && (
                <Badge
                  variant="secondary"
                  className="ml-1 h-5 px-1.5 rounded-full"
                >
                  {(filters.type !== "all" ? 1 : 0) +
                    filters.categories.length +
                    (!hideDateFilter &&
                    (filters.dateRange.from || filters.dateRange.to)
                      ? 1
                      : 0)}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="bottom" className="h-[85vh] rounded-t-xl">
            <SheetHeader className="mb-4">
              <SheetTitle>Filters & Sorting</SheetTitle>
            </SheetHeader>
            <div className="space-y-6 overflow-y-auto max-h-[calc(85vh-80px)] pb-20">
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Transaction Type</h3>
                <Select
                  value={filters.type}
                  onValueChange={(value) =>
                    setFilters({ ...filters, type: value })
                  }
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {Object.values(EXPENSE_TYPE).map((type) => (
                      <SelectItem key={type} value={type}>
                        {EXPENSE_MAP[type]}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-medium">Categories</h3>
                  {filters.categories.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFilters({ ...filters, categories: [] })}
                      className="h-auto py-1 px-2 text-xs"
                    >
                      Clear all
                    </Button>
                  )}
                </div>
                <div className="grid grid-cols-2 gap-3">
                  {categories.map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category}-mobile`}
                        checked={filters.categories.includes(category)}
                        onCheckedChange={() => handleCategoryToggle(category)}
                        className=""
                      />
                      <label
                        htmlFor={`category-${category}-mobile`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {category}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-sm font-medium">Sort By</h3>
                <Select
                  value={`${filters.sortBy}-${filters.sortDirection}`}
                  onValueChange={handleSortChange}
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">
                      Date (Newest First)
                    </SelectItem>
                    <SelectItem value="date-asc">
                      Date (Oldest First)
                    </SelectItem>
                    <SelectItem value="amount-desc">
                      Amount (High to Low)
                    </SelectItem>
                    <SelectItem value="amount-asc">
                      Amount (Low to High)
                    </SelectItem>
                    <SelectItem value="name-asc">Name (A-Z)</SelectItem>
                    <SelectItem value="name-desc">Name (Z-A)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {!hideDateFilter && (
                <div className="space-y-4">
                  <h3 className="text-sm font-medium">Date Range</h3>
                  <div className="w-full">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="date-range"
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal h-10",
                            !filters.dateRange.from &&
                              !filters.dateRange.to &&
                              "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {filters.dateRange.from && filters.dateRange.to ? (
                            <>
                              {format(filters.dateRange.from, "MMM d, yyyy")} -{" "}
                              {format(filters.dateRange.to, "MMM d, yyyy")}
                            </>
                          ) : filters.dateRange.from ? (
                            <>
                              From:{" "}
                              {format(filters.dateRange.from, "MMM d, yyyy")}
                            </>
                          ) : filters.dateRange.to ? (
                            <>
                              To: {format(filters.dateRange.to, "MMM d, yyyy")}
                            </>
                          ) : (
                            <span>Select date range</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 shadow-lg"
                        align="start"
                      >
                        <div className="p-3 border-b">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium">Date Range</h4>
                            {(filters.dateRange.from ||
                              filters.dateRange.to) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  setFilters({
                                    ...filters,
                                    dateRange: {
                                      from: undefined,
                                      to: undefined,
                                    },
                                  })
                                }
                                className="h-auto py-1 px-2 text-xs"
                              >
                                Clear
                              </Button>
                            )}
                          </div>
                          <div className="grid grid-cols-2 gap-2 mt-2">
                            <div>
                              <p className="text-xs text-muted-foreground mb-1">
                                From
                              </p>
                              <div className="p-2 border rounded-md text-sm">
                                {filters.dateRange.from
                                  ? format(
                                      filters.dateRange.from,
                                      "MMM d, yyyy"
                                    )
                                  : "Not set"}
                              </div>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground mb-1">
                                To
                              </p>
                              <div className="p-2 border rounded-md text-sm">
                                {filters.dateRange.to
                                  ? format(filters.dateRange.to, "MMM d, yyyy")
                                  : "Not set"}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="p-3">
                          <Calendar
                            mode="range"
                            selected={{
                              from: filters.dateRange.from,
                              to: filters.dateRange.to,
                            }}
                            onSelect={(range) => {
                              if (range) {
                                setFilters({
                                  ...filters,
                                  dateRange: {
                                    from: range.from,
                                    to: range.to,
                                  },
                                });
                              }
                            }}
                            numberOfMonths={1}
                            defaultMonth={filters.dateRange.from || new Date()}
                            fixedWeeks
                            showOutsideDays={false}
                          />
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              )}

              <div className="pt-4 sticky bottom-0 bg-background pb-4 mt-8">
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={handleReset}
                    className="flex-1"
                  >
                    Reset All
                  </Button>
                  <Button
                    onClick={() => setIsFiltersOpen(false)}
                    className="flex-1"
                  >
                    Apply Filters
                  </Button>
                </div>
              </div>
            </div>
          </SheetContent>
        </Sheet>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="flex items-center gap-2 h-10">
              {filters.sortDirection === "asc" ? (
                <ArrowUpIcon className="h-4 w-4" />
              ) : (
                <ArrowDownIcon className="h-4 w-4" />
              )}
              <span>Sort</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56">
            <DropdownMenuLabel>Sort by</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuRadioGroup
              value={`${filters.sortBy}-${filters.sortDirection}`}
              onValueChange={handleSortChange}
            >
              <DropdownMenuRadioItem value="date-desc">
                Date (Newest First)
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="date-asc">
                Date (Oldest First)
              </DropdownMenuRadioItem>
              <DropdownMenuSeparator />
              <DropdownMenuRadioItem value="amount-desc">
                Amount (High to Low)
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="amount-asc">
                Amount (Low to High)
              </DropdownMenuRadioItem>
              <DropdownMenuSeparator />
              <DropdownMenuRadioItem value="name-asc">
                Name (A-Z)
              </DropdownMenuRadioItem>
              <DropdownMenuRadioItem value="name-desc">
                Name (Z-A)
              </DropdownMenuRadioItem>
            </DropdownMenuRadioGroup>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Desktop filters */}
      <div className="hidden md:block">
        <Collapsible>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <CollapsibleTrigger asChild>
                <Button variant="outline" size="sm" className="h-9 gap-1">
                  <FilterIcon className="h-3.5 w-3.5" />
                  <span>Filters</span>
                  {hasActiveFilters && (
                    <Badge
                      variant="secondary"
                      className="ml-1 h-5 px-1.5 rounded-full"
                    >
                      {(filters.type !== "all" ? 1 : 0) +
                        filters.categories.length +
                        (!hideDateFilter &&
                        (filters.dateRange.from || filters.dateRange.to)
                          ? 1
                          : 0)}
                    </Badge>
                  )}
                </Button>
              </CollapsibleTrigger>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-9 gap-1">
                    {filters.sortDirection === "asc" ? (
                      <ArrowUpIcon className="h-3.5 w-3.5" />
                    ) : (
                      <ArrowDownIcon className="h-3.5 w-3.5" />
                    )}
                    <span>{getSortLabel()}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuRadioGroup
                    value={`${filters.sortBy}-${filters.sortDirection}`}
                    onValueChange={handleSortChange}
                  >
                    <DropdownMenuRadioItem value="date-desc">
                      Date (Newest First)
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="date-asc">
                      Date (Oldest First)
                    </DropdownMenuRadioItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuRadioItem value="amount-desc">
                      Amount (High to Low)
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="amount-asc">
                      Amount (Low to High)
                    </DropdownMenuRadioItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuRadioItem value="name-asc">
                      Name (A-Z)
                    </DropdownMenuRadioItem>
                    <DropdownMenuRadioItem value="name-desc">
                      Name (Z-A)
                    </DropdownMenuRadioItem>
                  </DropdownMenuRadioGroup>
                </DropdownMenuContent>
              </DropdownMenu>

              {hasActiveFilters && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleReset}
                  className="h-9 text-muted-foreground hover:text-foreground gap-1"
                >
                  <XIcon className="h-3.5 w-3.5" />
                  <span>Clear all</span>
                </Button>
              )}
            </div>

            {/* Active filters display */}
            {hasActiveFilters && (
              <div className="flex flex-wrap items-center gap-2">
                {filters.type !== "all" && (
                  <Badge variant="outline" className="h-6 gap-1 px-2">
                    Type: {filters.type.replace("_", " ")}
                    <XIcon
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => setFilters({ ...filters, type: "all" })}
                    />
                  </Badge>
                )}

                {/* {!hideDateFilter &&
                  (filters.dateRange.from || filters.dateRange.to) && (
                    <Badge variant="outline" className="h-6 gap-1 px-2">
                      {filters.dateRange.from && filters.dateRange.to ? (
                        <>
                          Date: {format(filters.dateRange.from, "MMM d")} -{" "}
                          {format(filters.dateRange.to, "MMM d, yyyy")}
                        </>
                      ) : filters.dateRange.from ? (
                        <>
                          From: {format(filters.dateRange.from, "MMM d, yyyy")}
                        </>
                      ) : (
                        <>To: {format(filters.dateRange.to, "MMM d, yyyy")}</>
                      )}
                      <XIcon
                        className="h-3 w-3 cursor-pointer ml-1"
                        onClick={() =>
                          setFilters({
                            ...filters,
                            dateRange: { from: undefined, to: undefined },
                          })
                        }
                      />
                    </Badge>
                  )} */}

                {filters.categories.length > 0 && (
                  <Badge variant="outline" className="h-6 gap-1 px-2">
                    Categories: {filters.categories.length}
                    <XIcon
                      className="h-3 w-3 cursor-pointer"
                      onClick={() => setFilters({ ...filters, categories: [] })}
                    />
                  </Badge>
                )}
              </div>
            )}
          </div>

          <CollapsibleContent className="mt-4 space-y-4">
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
            >
              <div className="space-y-2">
                <p className="text-sm font-medium">Transaction Type</p>
                <Select
                  value={filters.type}
                  onValueChange={(value) =>
                    setFilters({ ...filters, type: value })
                  }
                >
                  <SelectTrigger className="h-10">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="income">Income</SelectItem>
                    <SelectItem value="expense">Expense</SelectItem>
                    <SelectItem value="debt_bought">Debt Bought</SelectItem>
                    <SelectItem value="debt_given">Debt Given</SelectItem>
                    <SelectItem value="investment">Investment</SelectItem>
                    <SelectItem value="tax_paid">Tax Paid</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">Categories</p>
                  {filters.categories.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setFilters({ ...filters, categories: [] })}
                      className="h-auto py-1 px-2 text-xs"
                    >
                      Clear
                    </Button>
                  )}
                </div>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-between h-10"
                    >
                      {filters.categories.length > 0 ? (
                        <span>{filters.categories.length} selected</span>
                      ) : (
                        <span className="text-muted-foreground">
                          Select categories
                        </span>
                      )}
                      <ChevronDownIcon className="h-4 w-4 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    className="w-[220px] p-0 shadow-lg"
                    align="start"
                  >
                    <div className="p-2 max-h-[300px] overflow-y-auto">
                      {categories.map((category) => (
                        <div
                          key={category}
                          className="flex items-center space-x-2 p-2 hover:bg-muted/50 rounded-md"
                        >
                          <Checkbox
                            id={`category-${category}`}
                            checked={filters.categories.includes(category)}
                            onCheckedChange={() =>
                              handleCategoryToggle(category)
                            }
                          />
                          <label
                            htmlFor={`category-${category}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer flex-1"
                          >
                            {category}
                          </label>
                        </div>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>
                {filters.categories.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-2">
                    {filters.categories.map((category) => (
                      <Badge
                        key={category}
                        variant="secondary"
                        className="text-xs"
                      >
                        {category}
                      </Badge>
                    ))}
                  </div>
                )}
              </div>

              {/* {!hideDateFilter && (
                <div className="space-y-2 col-span-1 md:col-span-2">
                  <p className="text-sm font-medium">Date Range</p>
                  <div className="w-full">
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          id="date-range-desktop"
                          variant={"outline"}
                          className={cn(
                            "w-full justify-start text-left font-normal h-10",
                            !filters.dateRange.from &&
                              !filters.dateRange.to &&
                              "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {filters.dateRange.from && filters.dateRange.to ? (
                            <>
                              {format(filters.dateRange.from, "MMM d, yyyy")} -{" "}
                              {format(filters.dateRange.to, "MMM d, yyyy")}
                            </>
                          ) : filters.dateRange.from ? (
                            <>
                              From:{" "}
                              {format(filters.dateRange.from, "MMM d, yyyy")}
                            </>
                          ) : filters.dateRange.to ? (
                            <>
                              To: {format(filters.dateRange.to, "MMM d, yyyy")}
                            </>
                          ) : (
                            <span>Select date range</span>
                          )}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 shadow-lg"
                        align="start"
                      >
                        <div className="p-3 border-b">
                          <div className="flex items-center justify-between">
                            <h4 className="text-sm font-medium">Date Range</h4>
                            {(filters.dateRange.from ||
                              filters.dateRange.to) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() =>
                                  setFilters({
                                    ...filters,
                                    dateRange: {
                                      from: undefined,
                                      to: undefined,
                                    },
                                  })
                                }
                                className="h-auto py-1 px-2 text-xs"
                              >
                                Clear
                              </Button>
                            )}
                          </div>
                          <div className="grid grid-cols-2 gap-2 mt-2">
                            <div>
                              <p className="text-xs text-muted-foreground mb-1">
                                From
                              </p>
                              <div className="p-2 border rounded-md text-sm">
                                {filters.dateRange.from
                                  ? format(
                                      filters.dateRange.from,
                                      "MMM d, yyyy"
                                    )
                                  : "Not set"}
                              </div>
                            </div>
                            <div>
                              <p className="text-xs text-muted-foreground mb-1">
                                To
                              </p>
                              <div className="p-2 border rounded-md text-sm">
                                {filters.dateRange.to
                                  ? format(filters.dateRange.to, "MMM d, yyyy")
                                  : "Not set"}
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="p-3">
                          <Calendar
                            mode="range"
                            selected={{
                              from: filters.dateRange.from,
                              to: filters.dateRange.to,
                            }}
                            onSelect={(range) => {
                              if (range) {
                                setFilters({
                                  ...filters,
                                  dateRange: {
                                    from: range.from,
                                    to: range.to,
                                  },
                                });
                              }
                            }}
                            numberOfMonths={2}
                            defaultMonth={filters.dateRange.from || new Date()}
                            fixedWeeks
                            showOutsideDays={false}
                          />
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
              )} */}
            </motion.div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
}
