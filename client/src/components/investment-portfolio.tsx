import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Pencil, Trash2 } from "lucide-react";
import {
  Investment,
  INVESTMENT_PURPOSE,
  INVESTMENT_TYPE,
} from "@/services/investment.service";
import { formatCurrency } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface InvestmentPortfolioProps {
  investments: Investment[];
  isLoading: boolean;
  onEdit?: (investment: Investment) => void;
  onDelete?: (investment: Investment) => void;
}

export function InvestmentPortfolio({
  investments,
  isLoading,
  onEdit,
  onDelete,
}: InvestmentPortfolioProps) {
  const [sortConfig, setSortConfig] = useState<{
    key: keyof Investment;
    direction: "asc" | "desc";
  }>({
    key: "name",
    direction: "asc",
  });

  const sortedInvestments = [...investments].sort((a, b) => {
    if (a[sortConfig.key] < b[sortConfig.key]) {
      return sortConfig.direction === "asc" ? -1 : 1;
    }
    if (a[sortConfig.key] > b[sortConfig.key]) {
      return sortConfig.direction === "asc" ? 1 : -1;
    }
    return 0;
  });

  const requestSort = (key: keyof Investment) => {
    let direction: "asc" | "desc" = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }
    setSortConfig({ key, direction });
  };

  const getInvestmentTypeLabel = (type: INVESTMENT_TYPE) => {
    switch (type) {
      case INVESTMENT_TYPE.STOCK:
        return "Stock";
      case INVESTMENT_TYPE.MUTUAL_FUND:
        return "Mutual Fund";
      case INVESTMENT_TYPE.GOLD:
        return "Gold";
      case INVESTMENT_TYPE.SILVER:
        return "Silver";
      default:
        return type;
    }
  };

  const getInvestmentPurposeLabel = (purpose: INVESTMENT_PURPOSE) => {
    switch (purpose) {
      case INVESTMENT_PURPOSE.OWNED:
        return "Owned";
      case INVESTMENT_PURPOSE.MONITORING:
        return "Monitoring";
      default:
        return purpose;
    }
  };

  const getPurposeBadgeVariant = (purpose: INVESTMENT_PURPOSE) => {
    switch (purpose) {
      case INVESTMENT_PURPOSE.OWNED:
        return "default";
      case INVESTMENT_PURPOSE.MONITORING:
        return "outline";
      default:
        return "secondary";
    }
  };

  const getTypeBadgeVariant = (type: INVESTMENT_TYPE) => {
    switch (type) {
      case INVESTMENT_TYPE.STOCK:
        return "default";
      case INVESTMENT_TYPE.MUTUAL_FUND:
        return "secondary";
      case INVESTMENT_TYPE.GOLD:
        return "destructive";
      case INVESTMENT_TYPE.SILVER:
        return "outline";
      default:
        return "secondary";
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-pulse space-y-4">
          <div className="h-4 bg-gray-200 rounded w-32"></div>
          <div className="h-4 bg-gray-200 rounded w-64"></div>
          <div className="h-4 bg-gray-200 rounded w-48"></div>
        </div>
      </div>
    );
  }

  if (investments.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <h3 className="text-lg font-medium mb-2">No investments found</h3>
        <p className="text-muted-foreground mb-4">
          You haven't added any investments yet.
        </p>
      </div>
    );
  }

  type ColumnConfig = {
    key: keyof Investment | "actions" | "value" | "profit";
    label: string;
    sortable: boolean;
    align?: "left" | "right";
    renderCell: (investment: Investment) => React.ReactNode;
  };

  const columns: ColumnConfig[] = [
    {
      key: "name",
      label: "Name",
      sortable: true,
      align: "left",
      renderCell: (investment) => (
        <span className="font-medium">{investment.name}</span>
      ),
    },
    {
      key: "symbol",
      label: "Symbol",
      sortable: true,
      align: "left",
      renderCell: (investment) => investment.symbol,
    },
    {
      key: "type",
      label: "Type",
      sortable: true,
      align: "left",
      renderCell: (investment) => (
        <Badge variant={getTypeBadgeVariant(investment.type)}>
          {getInvestmentTypeLabel(investment.type)}
        </Badge>
      ),
    },
    {
      key: "purpose",
      label: "Purpose",
      sortable: true,
      align: "left",
      renderCell: (investment) => (
        <Badge variant={getPurposeBadgeVariant(investment.purpose)}>
          {getInvestmentPurposeLabel(investment.purpose)}
        </Badge>
      ),
    },
    {
      key: "purchasedPrice",
      label: "Purchased Price",
      sortable: true,
      align: "right",
      renderCell: (investment) => formatCurrency(investment.purchasedPrice),
    },
    // Show quantity for owned investments
    {
      key: "quantity",
      label: "Quantity",
      sortable: true,
      align: "right",
      renderCell: (investment) => {
        if (investment.purpose === INVESTMENT_PURPOSE.OWNED) {
          // If we have totalQuantity from backend, use it
          if (investment.totalQuantity) {
            return investment.totalQuantity;
          }
          // If we have purchases array, calculate total quantity
          else if (investment.purchases && investment.purchases.length > 0) {
            const totalQuantity = investment.purchases.reduce(
              (sum, purchase) => sum + purchase.quantity,
              0
            );
            return totalQuantity;
          }
          // Fallback to legacy quantity field
          else if (investment.quantity) {
            return investment.quantity;
          }
        }
        return "-";
      },
    },
    // Show current price for owned stocks
    {
      key: "currentPrice",
      label: "Current Price",
      sortable: false,
      align: "right",
      renderCell: (investment) => {
        // For owned stocks, show current price per share
        if (
          investment.purpose === INVESTMENT_PURPOSE.OWNED &&
          investment.type === INVESTMENT_TYPE.STOCK
        ) {
          // If we have current price data, show it
          if (investment.currentPrice) {
            // Show the current price per share
            return formatCurrency(investment.currentPrice);
          }
          // Fallback to average purchase price if no current price available
          if (investment.avgPurchasePrice) {
            return formatCurrency(investment.avgPurchasePrice);
          }
          // Fallback to purchased price if no average price available
          if (investment.purchasedPrice) {
            return formatCurrency(investment.purchasedPrice);
          }
        }
        return "-";
      },
    },
    // Show purchase details for owned investments
    {
      key: "purchases",
      label: "Purchase Details",
      sortable: false,
      align: "right",
      renderCell: (investment) => {
        if (investment.purpose === INVESTMENT_PURPOSE.OWNED) {
          // If we have purchases array, show details of all purchases
          if (investment.purchases && investment.purchases.length > 0) {
            return (
              <div className="text-xs space-y-1">
                {investment.purchases.map((purchase, index) => (
                  <div key={index} className="flex justify-end gap-2">
                    <span>{purchase.quantity} @ </span>
                    <span>{formatCurrency(purchase.price)}</span>
                  </div>
                ))}
              </div>
            );
          }
          // Fallback to legacy fields
          else if (investment.purchasedPrice && investment.quantity) {
            return (
              <div className="text-xs">
                <div className="flex justify-end gap-2">
                  <span>{investment.quantity} @ </span>
                  <span>{formatCurrency(investment.purchasedPrice)}</span>
                </div>
              </div>
            );
          }
        }
        return "-";
      },
    },
    // Add profit column for owned stocks
    {
      key: "profit",
      label: "Profit/Loss",
      sortable: false,
      align: "right",
      renderCell: (investment) => {
        // Only show profit for owned stocks
        if (
          investment.purpose === INVESTMENT_PURPOSE.OWNED &&
          investment.type === INVESTMENT_TYPE.STOCK
        ) {
          // If we have totalProfit from the backend, use it
          if (investment.totalProfit !== undefined) {
            const isPositive = investment.totalProfit >= 0;
            const colorClass = isPositive ? "text-green-600" : "text-red-600";
            const prefix = isPositive ? "+" : ""; // Add plus sign for positive values
            return (
              <span className={`font-medium ${colorClass}`}>
                {prefix}
                {formatCurrency(investment.totalProfit)}
              </span>
            );
          }
          // If we have currentPrice but no totalProfit, calculate it
          else if (investment.currentPrice) {
            // Calculate based on purchases array if available
            if (investment.purchases && investment.purchases.length > 0) {
              let purchasedValue = 0;
              let totalQuantity = 0;

              // Calculate total investment value and quantity
              investment.purchases.forEach((purchase) => {
                purchasedValue += purchase.price * purchase.quantity;
                totalQuantity += purchase.quantity;
              });

              const currentValue = investment.currentPrice * totalQuantity;
              const profit = currentValue - purchasedValue;

              const isPositive = profit >= 0;
              const colorClass = isPositive ? "text-green-600" : "text-red-600";
              const prefix = isPositive ? "+" : "";

              return (
                <span className={`font-medium ${colorClass}`}>
                  {prefix}
                  {formatCurrency(profit)}
                </span>
              );
            }
            // Fallback to legacy fields
            else if (investment.purchasedPrice && investment.quantity) {
              const purchasedValue =
                investment.purchasedPrice * investment.quantity;
              const currentValue =
                investment.currentPrice * investment.quantity;
              const profit = currentValue - purchasedValue;

              const isPositive = profit >= 0;
              const colorClass = isPositive ? "text-green-600" : "text-red-600";
              const prefix = isPositive ? "+" : "";

              return (
                <span className={`font-medium ${colorClass}`}>
                  {prefix}
                  {formatCurrency(profit)}
                </span>
              );
            }
          }
        }
        return "-";
      },
    },
    {
      key: "actions",
      label: "Actions",
      sortable: false,
      align: "right",
      renderCell: (investment) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onEdit && onEdit(investment)}>
              <Pencil className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onDelete && onDelete(investment)}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const renderHeaderCell = (column: ColumnConfig) => {
    const className = `${column.sortable ? "cursor-pointer" : ""} ${
      column.align === "right" ? "text-right" : ""
    }`;

    return (
      <TableHead
        key={column.key}
        className={className}
        onClick={() =>
          column.sortable && requestSort(column.key as keyof Investment)
        }
      >
        {column.label}
        {column.sortable && sortConfig.key === column.key && (
          <span className="ml-1">
            {sortConfig.direction === "asc" ? "↑" : "↓"}
          </span>
        )}
      </TableHead>
    );
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>{columns.map(renderHeaderCell)}</TableRow>
        </TableHeader>
        <TableBody>
          {sortedInvestments.map((investment) => (
            <TableRow key={investment._id}>
              {columns.map((column) => (
                <TableCell
                  key={`${investment._id}-${String(column.key)}`}
                  className={column.align === "right" ? "text-right" : ""}
                >
                  {column.renderCell(investment)}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
