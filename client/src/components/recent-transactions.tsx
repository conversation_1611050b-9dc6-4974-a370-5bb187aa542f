import { useEffect, useState } from "react";
import {
  ArrowDownIcon,
  ArrowUpIcon,
  CreditCardIcon,
  DollarSignIcon,
  TrendingDownIcon,
  TrendingUpIcon,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { EXPENSE_TYPE, getExpenseStats } from "@/services/expense.service";
import { Skeleton } from "@/components/ui/skeleton";

const typeIcons = {
  income: <ArrowUpIcon className="h-4 w-4 text-emerald-500" />,
  expense: <ArrowDownIcon className="h-4 w-4 text-rose-500" />,
  debt_bought: <TrendingDownIcon className="h-4 w-4 text-amber-500" />,
  debt_given: <TrendingUpIcon className="h-4 w-4 text-amber-500" />,
  investment: <DollarSignIcon className="h-4 w-4 text-blue-500" />,
  tax_paid: <CreditCardIcon className="h-4 w-4 text-purple-500" />,
};

const typeColors = {
  income: "bg-emerald-100 text-emerald-800",
  expense: "bg-rose-100 text-rose-800",
  debt_bought: "bg-amber-100 text-amber-800",
  debt_given: "bg-amber-100 text-amber-800",
  investment: "bg-blue-100 text-blue-800",
  tax_paid: "bg-purple-100 text-purple-800",
};

import { Expense } from "@/services/expense.service";

interface RecentTransactionsProps {
  transactions?: Expense[];
  isLoading?: boolean;
}

export function RecentTransactions({
  transactions: propTransactions,
  isLoading = false,
}: RecentTransactionsProps) {
  const [transactions, setTransactions] = useState<Expense[]>([]);
  const [loading, setLoading] = useState(isLoading);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (propTransactions) {
      setTransactions(propTransactions);
      setLoading(false);
      setError(false);
    }
  }, [propTransactions]);

  useEffect(() => {
    if (!propTransactions) {
      const fetchTransactions = async () => {
        try {
          setLoading(true);
          const stats = await getExpenseStats();
          setTransactions(stats.recentTransactions);
          setError(false);
        } catch (err) {
          console.error("Failed to fetch transactions:", err);
          setTransactions([]);
          setError(true);
        } finally {
          setLoading(false);
        }
      };

      fetchTransactions();
    }
  }, [propTransactions]);

  if (loading) {
    return (
      <div className="rounded-md border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50 hover:bg-muted/50">
              <TableHead className="font-medium">Type</TableHead>
              <TableHead className="font-medium">Name</TableHead>
              <TableHead className="font-medium">Category</TableHead>
              <TableHead className="font-medium">Date</TableHead>
              <TableHead className="text-right font-medium">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[1, 2, 3].map((i) => (
              <TableRow key={i} className="hover:bg-muted/30">
                <TableCell>
                  <Skeleton className="h-6 w-20" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-20" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-6 w-24" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-6 w-16 ml-auto" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-md border p-4 text-center text-muted-foreground">
        Failed to load transactions. Please try again later.
      </div>
    );
  }

  return (
    <div className="rounded-md border overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50 hover:bg-muted/50">
            <TableHead className="font-medium">Type</TableHead>
            <TableHead className="font-medium">Name</TableHead>
            <TableHead className="font-medium">Category</TableHead>
            <TableHead className="font-medium">Date</TableHead>
            <TableHead className="text-right font-medium">Amount</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.length === 0 ? (
            <TableRow>
              <TableCell
                colSpan={5}
                className="text-center py-4 text-muted-foreground"
              >
                No transactions found. Add your first transaction!
              </TableCell>
            </TableRow>
          ) : (
            transactions.map((transaction) => (
              <TableRow key={transaction._id} className="hover:bg-muted/30">
                <TableCell>
                  <div className="flex items-center gap-2">
                    {typeIcons[transaction.type]}
                    <Badge
                      variant="outline"
                      className={`${
                        typeColors[transaction.type]
                      } px-2 py-0.5 text-xs font-medium`}
                    >
                      {transaction.type.replace("_", " ")}
                    </Badge>
                  </div>
                </TableCell>
                <TableCell className="font-medium">
                  {transaction.name || "Unnamed"}
                </TableCell>
                <TableCell>{transaction.category}</TableCell>
                <TableCell>
                  {new Date(transaction.eventDate).toLocaleDateString()}
                </TableCell>
                <TableCell
                  className={`text-right font-medium ${
                    transaction.type === EXPENSE_TYPE.INCOME
                      ? "text-emerald-600"
                      : transaction.type === EXPENSE_TYPE.EXPENSE
                      ? "text-rose-600"
                      : transaction.type === EXPENSE_TYPE.INVESTMENT
                      ? "text-blue-600"
                      : transaction.type.includes("debt")
                      ? "text-amber-600"
                      : ""
                  }`}
                >
                  {transaction.type === EXPENSE_TYPE.INCOME
                    ? "+"
                    : transaction.type === EXPENSE_TYPE.EXPENSE
                    ? "-"
                    : ""}
                  ${Math.abs(transaction.amount).toFixed(2)}
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}
