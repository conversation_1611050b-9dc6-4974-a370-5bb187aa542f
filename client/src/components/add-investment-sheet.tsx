import { useState, useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import { z } from "zod";
import { Loader2, Plus, Trash2 } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

import {
  createInvestment,
  updateInvestment,
  INVESTMENT_PURPOSE,
  INVESTMENT_TYPE,
  Investment,
  Purchase,
} from "@/services/investment.service";
import { toast } from "@/components/ui/use-toast";
import Render<PERSON>hen from "./Renderwhen";

// Define the purchase schema for validation
const purchaseSchema = z.object({
  price: z.coerce.number().positive({
    message: "Price must be a positive number.",
  }),
  quantity: z.coerce.number().positive({
    message: "Quantity must be a positive number.",
  }),
});

const formSchema = z
  .object({
    name: z.string().min(2, {
      message: "Name must be at least 2 characters.",
    }),
    symbol: z.string().min(1, {
      message: "Symbol is required.",
    }),
    // Legacy fields kept for backward compatibility
    purchasedPrice: z.coerce
      .number()
      .positive({
        message: "Price must be a positive number.",
      })
      .optional(),
    quantity: z.coerce.number().optional(),
    // New field for multiple purchases
    purchases: z.array(purchaseSchema).optional(),
    type: z.enum([
      INVESTMENT_TYPE.STOCK,
      INVESTMENT_TYPE.MUTUAL_FUND,
      INVESTMENT_TYPE.GOLD,
      INVESTMENT_TYPE.SILVER,
    ]),
    purpose: z.enum([INVESTMENT_PURPOSE.MONITORING, INVESTMENT_PURPOSE.OWNED]),
    targetPrice: z.coerce.number().optional(),
  })
  .refine(
    (data) => {
      // If purpose is OWNED, either purchasedPrice or purchases is required
      if (data.purpose === INVESTMENT_PURPOSE.OWNED) {
        return (
          data.purchasedPrice !== undefined ||
          (data.purchases !== undefined && data.purchases.length > 0)
        );
      }
      return true;
    },
    {
      message: "Purchase information is required for owned investments",
      path: ["purchases"],
    }
  );

interface AddInvestmentSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  selectedInvestment?: Investment | null;
}
type SelectOption = {
  value: string;
  label: string;
};

type FormFieldConfig = {
  name: string;
  label: string;
  type: "input" | "select";
  placeholder: string;
  description: string;
  inputType?: string;
  options?: SelectOption[];
  showWhen: () => boolean;
};
export function AddInvestmentSheet({
  open,
  onOpenChange,
  onSuccess,
  selectedInvestment,
}: AddInvestmentSheetProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      symbol: "",
      type: INVESTMENT_TYPE.STOCK,
      purpose: INVESTMENT_PURPOSE.OWNED,
      quantity: undefined,
      purchases: [{ price: undefined, quantity: undefined }],
    },
  });

  // Setup field array for purchases
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "purchases",
  });

  const purpose = form.watch("purpose");

  // Reset form when purpose changes to monitoring
  useEffect(() => {
    if (purpose === INVESTMENT_PURPOSE.MONITORING) {
      form.setValue("purchasedPrice", undefined);
      form.setValue("quantity", undefined);
      form.setValue("purchases", []);
    } else if (fields.length === 0) {
      // Add a default empty purchase entry if switching to OWNED
      append({ price: undefined, quantity: undefined });
    }
  }, [purpose, form, fields.length, append]);

  // Populate form with selected investment data when in edit mode
  useEffect(() => {
    if (selectedInvestment) {
      // If the investment has purchases array, use it
      // Otherwise, create a purchase entry from the legacy fields
      const purchases = selectedInvestment.purchases?.length
        ? selectedInvestment.purchases
        : selectedInvestment.purchasedPrice && selectedInvestment.quantity
        ? [
            {
              price: selectedInvestment.purchasedPrice,
              quantity: selectedInvestment.quantity,
            },
          ]
        : [{ price: undefined, quantity: undefined }];

      form.reset({
        name: selectedInvestment.name,
        symbol: selectedInvestment.symbol,
        type: selectedInvestment.type as
          | INVESTMENT_TYPE.STOCK
          | INVESTMENT_TYPE.MUTUAL_FUND
          | INVESTMENT_TYPE.GOLD
          | INVESTMENT_TYPE.SILVER,
        purpose: selectedInvestment.purpose,
        purchasedPrice: selectedInvestment.purchasedPrice,
        quantity: selectedInvestment.quantity,
        purchases: purchases,
        targetPrice: selectedInvestment.targetPrice,
      });
    } else {
      form.reset({
        name: "",
        symbol: "",
        type: INVESTMENT_TYPE.STOCK,
        purpose: INVESTMENT_PURPOSE.OWNED,
        quantity: undefined,
        purchases: [{ price: undefined, quantity: undefined }],
      });
    }
  }, [selectedInvestment, form]);

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsSubmitting(true);

      // Filter out any empty purchase entries
      const validPurchases = values.purchases?.filter(
        (p) => p.price !== undefined && p.quantity !== undefined
      );

      // Map form values to the Investment interface
      const investment: Investment = {
        name: values.name,
        symbol: values.symbol,
        type: values.type,
        purpose: values.purpose,
        targetPrice: values.targetPrice,
      };

      // If we have valid purchases, use them
      if (validPurchases && validPurchases.length > 0) {
        investment.purchases = validPurchases;

        // For backward compatibility, also set the first purchase as the main price/quantity
        investment.purchasedPrice = validPurchases[0].price;
        investment.quantity = validPurchases[0].quantity;
      } else if (values.purchasedPrice && values.quantity) {
        // If we have legacy fields, use them
        investment.purchasedPrice = values.purchasedPrice;
        investment.quantity = values.quantity;
        investment.purchases = [
          { price: values.purchasedPrice, quantity: values.quantity },
        ];
      } else if (values.purpose === INVESTMENT_PURPOSE.MONITORING) {
        // For monitoring investments, set default values
        investment.purchasedPrice = 0;
        investment.purchases = [];
      }

      // If we have a selected investment, update it; otherwise create a new one
      if (selectedInvestment?._id) {
        await updateInvestment(selectedInvestment._id, investment);
        toast({
          title: "Investment updated",
          description: "Your investment has been updated successfully.",
        });
      } else {
        await createInvestment(investment);
        toast({
          title: "Investment added",
          description: "Your investment has been added successfully.",
        });
      }

      form.reset();
      onOpenChange(false);
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error(
        `Failed to ${selectedInvestment ? "update" : "add"} investment:`,
        error
      );
      toast({
        title: "Error",
        description: `Failed to ${
          selectedInvestment ? "update" : "add"
        } investment. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  const formFields: FormFieldConfig[] = [
    {
      name: "name",
      label: "Name",
      type: "input",
      placeholder: "HDFC Bank",
      description: "Enter the name of the investment.",
      showWhen: () => true,
    },
    {
      name: "symbol",
      label: "Symbol",
      type: "input",
      placeholder: "HDFCBANK",
      description: "Enter the stock name in stock market format",
      showWhen: () => true,
    },
    {
      name: "purpose",
      label: "Purpose",
      type: "select",
      placeholder: "Select purpose",
      description: "Are you tracking this investment or do you own it?",
      options: [
        { value: INVESTMENT_PURPOSE.OWNED, label: "Owned" },
        { value: INVESTMENT_PURPOSE.MONITORING, label: "Monitoring" },
      ],
      showWhen: () => true,
    },
    {
      name: "type",
      label: "Type",
      type: "select",
      placeholder: "Select investment type",
      description: "Select the type of investment.",
      options: [
        { value: INVESTMENT_TYPE.STOCK, label: "Stock" },
        { value: INVESTMENT_TYPE.MUTUAL_FUND, label: "Mutual Fund" },
        { value: INVESTMENT_TYPE.GOLD, label: "Gold" },
        { value: INVESTMENT_TYPE.SILVER, label: "Silver" },
      ],
      showWhen: () => true,
    },
    {
      name: "targetPrice",
      label: "Target Price",
      type: "input",
      inputType: "number",
      placeholder: "1800.00",
      description: "Enter your target price (optional).",
      showWhen: () => true,
    },
  ];

  const renderFormField = (fieldConfig: FormFieldConfig) => {
    if (!fieldConfig.showWhen()) {
      return null;
    }

    return (
      <FormField
        key={fieldConfig.name}
        control={form.control}
        name={fieldConfig.name as keyof z.infer<typeof formSchema>}
        render={({ field }) => (
          <FormItem>
            <FormLabel>{fieldConfig.label}</FormLabel>
            <FormControl>
              {fieldConfig.type === "input" ? (
                <Input
                  type={fieldConfig.inputType || "text"}
                  placeholder={fieldConfig.placeholder}
                  {...field}
                  value={
                    field.value !== undefined && field.value !== null
                      ? field.value
                      : ""
                  }
                />
              ) : fieldConfig.type === "select" ? (
                <Select
                  onValueChange={field.onChange}
                  defaultValue={String(field.value)}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder={fieldConfig.placeholder} />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {fieldConfig.options?.map((option: SelectOption) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : null}
            </FormControl>
            <FormDescription>{fieldConfig.description}</FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[425px] overflow-y-auto">
        <SheetHeader>
          <SheetTitle>
            {selectedInvestment ? "Update Investment" : "Add Investment"}
          </SheetTitle>
          <SheetDescription>
            {selectedInvestment
              ? "Update the details of your investment."
              : "Add a new investment to your portfolio."}
          </SheetDescription>
        </SheetHeader>
        <div className="py-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {formFields.map(renderFormField)}

              {/* Render purchases section for owned investments */}
              {purpose === INVESTMENT_PURPOSE.OWNED && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-medium">Purchases</h3>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        append({ price: undefined, quantity: undefined })
                      }
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Purchase
                    </Button>
                  </div>

                  {fields.map((field, index) => (
                    <div key={field.id} className="flex gap-2 items-start">
                      <div className="flex-1">
                        <FormField
                          control={form.control}
                          name={
                            `purchases.${index}.price` as `purchases.${number}.price`
                          }
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Price</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="1500.00"
                                  {...field}
                                  value={field.value || ""}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      <div className="flex-1">
                        <FormField
                          control={form.control}
                          name={
                            `purchases.${index}.quantity` as `purchases.${number}.quantity`
                          }
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Quantity</FormLabel>
                              <FormControl>
                                <Input
                                  type="number"
                                  placeholder="10"
                                  {...field}
                                  value={field.value || ""}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="mt-8"
                          onClick={() => remove(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}

              <Button type="submit" className="w-full" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {selectedInvestment ? "Updating..." : "Adding..."}
                  </>
                ) : selectedInvestment ? (
                  "Update Investment"
                ) : (
                  "Add Investment"
                )}
              </Button>
            </form>
          </Form>
        </div>
      </SheetContent>
    </Sheet>
  );
}
