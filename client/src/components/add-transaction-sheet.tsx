import { useState } from "react";
import { CalendarIcon, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { createExpense, Expense } from "@/services/expense.service";
import { useToast } from "@/components/ui/use-toast";

interface AddTransactionSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const formSchema = z.object({
  type: z.enum(
    [
      "INCOME",
      "EXPENSE",
      "DEBT_BOUGHT",
      "DEBT_GIVEN",
      "INVESTMENT",
      "INCOME_TAX",
    ],
    {
      errorMap: () => ({ message: "Transaction type is required" }),
    }
  ),
  name: z.string(),
  category: z.string().min(1, "Category is required"),
  amount: z.string().min(1, "Amount is required"),
  note: z.string().optional(),
  eventDate: z.date(),
});

type FormValues = z.infer<typeof formSchema>;

type SelectOption = {
  value: string;
  label: string;
};

type FormFieldConfig = {
  name: keyof FormValues;
  label: string;
  type: "input" | "select" | "textarea" | "date";
  placeholder?: string;
  description?: string;
  inputType?: string;
  options?: SelectOption[];
  className?: string;
  showWhen?: (type: string) => boolean;
};

export function AddTransactionSheet({
  open,
  onOpenChange,
  onSuccess,
}: AddTransactionSheetProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      type: "EXPENSE" as const,
      name: "",
      category: "",
      amount: "",
      note: "",
      eventDate: new Date(),
    },
  });

  const transactionType = form.watch("type");

  const onSubmit = async (data: FormValues) => {
    try {
      setIsSubmitting(true);

      const amountValue = parseFloat(data.amount);

      const adjustedAmount =
        data.type === "EXPENSE" || data.type === "DEBT_BOUGHT"
          ? -Math.abs(amountValue)
          : Math.abs(amountValue);

      const expenseData: Expense = {
        ...data,
        amount: adjustedAmount,
      } as Expense;

      await createExpense(expenseData);

      toast({
        title: "Transaction added",
        description: "Your transaction has been successfully added.",
        variant: "default",
      });

      onOpenChange(false);
      form.reset();

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Failed to add transaction:", error);
      toast({
        title: "Error",
        description: "Failed to add transaction. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const formFields: FormFieldConfig[] = [
    {
      name: "type",
      label: "Transaction Type",
      type: "select",
      placeholder: "Select transaction type",
      options: [
        { value: "INCOME", label: "Income" },
        { value: "EXPENSE", label: "Expense" },
        { value: "DEBT_BOUGHT", label: "Debt Bought" },
        { value: "DEBT_GIVEN", label: "Debt Given" },
        { value: "INVESTMENT", label: "Investment" },
        { value: "INCOME_TAX", label: "Tax Paid" },
      ],
    },
    {
      name: "name",
      label: "Name",
      type: "input",
      placeholder: "e.g. Salary, Rent, Groceries",
      description: "Name of the transaction",
    },
    {
      name: "category",
      label: "Category",
      type: "select",
      placeholder: "Select category",
      options: [
        { value: "Income", label: "Income" },
        { value: "Housing", label: "Housing" },
        { value: "Food", label: "Food" },
        { value: "Transportation", label: "Transportation" },
        { value: "Entertainment", label: "Entertainment" },
        { value: "Utilities", label: "Utilities" },
        { value: "Debt", label: "Debt" },
        { value: "Investment", label: "Investment" },
        { value: "Tax", label: "Tax" },
        { value: "Other", label: "Other" },
      ],
    },
    {
      name: "amount",
      label: "Amount",
      type: "input",
      inputType: "number",
      placeholder: "0.00",
    },
    {
      name: "eventDate",
      label: "Date",
      type: "date",
      className: "flex flex-col",
    },
    {
      name: "note",
      label: "Note",
      type: "textarea",
      placeholder: "Add any additional details about this transaction",
    },
  ];

  const renderFormField = (fieldConfig: FormFieldConfig) => {
    if (fieldConfig.showWhen && !fieldConfig.showWhen(transactionType)) {
      return null;
    }

    return (
      <FormField
        key={String(fieldConfig.name)}
        control={form.control}
        name={fieldConfig.name}
        render={({ field }) => {
          const renderFieldControl = () => {
            switch (fieldConfig.type) {
              case "input":
                return (
                  <Input
                    type={fieldConfig.inputType || "text"}
                    placeholder={fieldConfig.placeholder}
                    {...field}
                    value={typeof field.value === "string" ? field.value : ""}
                  />
                );

              case "select":
                return (
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={String(field.value)}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={fieldConfig.placeholder} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {fieldConfig.options?.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                );

              case "textarea":
                return (
                  <Textarea
                    placeholder={fieldConfig.placeholder}
                    className="resize-none"
                    {...field}
                    value={typeof field.value === "string" ? field.value : ""}
                  />
                );

              case "date":
                return (
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant={"outline"}
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value instanceof Date ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Pick a date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={
                          field.value instanceof Date ? field.value : undefined
                        }
                        onSelect={field.onChange}
                      />
                    </PopoverContent>
                  </Popover>
                );

              default:
                return null;
            }
          };

          return (
            <FormItem className={fieldConfig.className}>
              <FormLabel>{fieldConfig.label}</FormLabel>
              <FormControl>{renderFieldControl()}</FormControl>
              {fieldConfig.description && (
                <FormDescription>{fieldConfig.description}</FormDescription>
              )}
              <FormMessage />
            </FormItem>
          );
        }}
      />
    );
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-[500px] w-full">
        <SheetHeader>
          <SheetTitle>Add New Transaction</SheetTitle>
          <SheetDescription>
            Add a new transaction to track your finances.
          </SheetDescription>
        </SheetHeader>
        <div className="py-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {formFields.map(renderFormField)}

              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Transaction"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </SheetContent>
    </Sheet>
  );
}
