"use client";

import { useState, useEffect } from "react";
import {
  ArrowDownIcon,
  ArrowUpIcon,
  CreditCardIcon,
  DollarSignIcon,
  MoreHorizontalIcon,
  PencilIcon,
  TrashIcon,
  TrendingDownIcon,
  TrendingUpIcon,
  EyeIcon,
} from "lucide-react";
import { useExpense } from "@/contexts/ExpenseContext";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TransactionDetailsSheet } from "@/components/transaction-details-sheet";
import {
  EditTransactionSheet,
  EditTransactionSheetProps,
} from "@/components/edit-transaction-sheet";
import { motion, AnimatePresence } from "framer-motion";
import { Skeleton } from "@/components/ui/skeleton";
import {
  deleteExpense,
  Expense,
  EXPENSE_TYPE,
} from "@/services/expense.service";
import { useToast } from "@/components/ui/use-toast";

const typeIcons = {
  income: <ArrowUpIcon className="h-4 w-4 text-emerald-500" />,
  expense: <ArrowDownIcon className="h-4 w-4 text-rose-500" />,
  debt_bought: <TrendingDownIcon className="h-4 w-4 text-amber-500" />,
  debt_given: <TrendingUpIcon className="h-4 w-4 text-amber-500" />,
  investment: <DollarSignIcon className="h-4 w-4 text-blue-500" />,
  tax_paid: <CreditCardIcon className="h-4 w-4 text-purple-500" />,
};

const typeColors = {
  income: "bg-emerald-100 text-emerald-800",
  expense: "bg-rose-100 text-rose-800",
  debt_bought: "bg-amber-100 text-amber-800",
  debt_given: "bg-amber-100 text-amber-800",
  investment: "bg-blue-100 text-blue-800",
  tax_paid: "bg-purple-100 text-purple-800",
};

const categoryIcons = {
  Income: "💰",
  Housing: "🏠",
  Food: "🍔",
  Transportation: "🚗",
  Entertainment: "🎬",
  Utilities: "💡",
  Debt: "💳",
  Investment: "📈",
  Tax: "📝",
  Other: "📦",
};

interface TransactionsTableProps {
  filters: {
    type: string;
    categories: string[];
    dateRange: {
      from: Date | undefined;
      to: Date | undefined;
    };
    searchQuery: string;
    sortBy: string;
    sortDirection: "asc" | "desc";
    ignoreDate: boolean;
  };
  transactions: Expense[];
  isLoading: boolean;
}

export function TransactionsTable({
  filters,
  transactions,
  isLoading,
}: TransactionsTableProps) {
  const { refreshData } = useExpense();
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedTransaction, setSelectedTransaction] =
    useState<EditTransactionSheetProps["transaction"]>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isEditOpen, setIsEditOpen] = useState(false);
  const { toast } = useToast();
  const itemsPerPage = 10;

  // Pagination
  const totalPages = Math.ceil(transactions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedTransactions = transactions.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const handleViewDetails = (transaction: Expense) => {
    setSelectedTransaction({
      id: transaction._id,
      type: transaction.type,
      name: transaction.name || "",
      category: transaction.category,
      amount: transaction.amount,
      date: transaction.eventDate
        ? new Date(transaction.eventDate).toISOString().split("T")[0]
        : "",
      note: transaction.note || "",
    });
    setIsDetailsOpen(true);
  };

  const handleEdit = (transaction: Expense) => {
    setSelectedTransaction({
      id: transaction._id,
      type: transaction.type,
      name: transaction.name || "",
      category: transaction.category,
      amount: transaction.amount,
      date: transaction.eventDate
        ? new Date(transaction.eventDate).toISOString().split("T")[0]
        : "",
      note: transaction.note || "",
    });
    setIsEditOpen(true);
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteExpense(id);

      // Refresh data in the context to trigger re-fetch
      refreshData();

      toast({
        title: "Success",
        description: "Transaction deleted successfully",
      });
    } catch (err) {
      console.error("Failed to delete transaction:", err);
      toast({
        title: "Error",
        description: "Failed to delete transaction. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return <TransactionTableSkeleton />;
  }

  return (
    <div className="space-y-4">
      {transactions.length === 0 ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="flex h-[300px] w-full items-center justify-center rounded-xl border border-dashed p-8"
        >
          <div className="flex flex-col items-center justify-center space-y-3 text-center">
            <div className="rounded-full bg-muted p-3">
              <SearchIcon className="h-6 w-6 text-muted-foreground" />
            </div>
            <h3 className="text-lg font-semibold">No transactions found</h3>
            <p className="text-sm text-muted-foreground max-w-[250px]">
              Try adjusting your filters or add a new transaction to get
              started.
            </p>
          </div>
        </motion.div>
      ) : (
        <>
          {/* Desktop view - Table */}
          <div className="hidden md:block rounded-xl border overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-muted/50 hover:bg-muted/50">
                  <TableHead>Type</TableHead>
                  <TableHead>Name</TableHead>
                  <TableHead>Category</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead className="w-[80px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <AnimatePresence>
                  {paginatedTransactions.map((transaction, index) => (
                    <motion.tr
                      key={transaction._id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0 }}
                      transition={{ delay: index * 0.05, duration: 0.2 }}
                      className="border-b hover:bg-muted/50 cursor-pointer"
                      onClick={() => handleViewDetails(transaction)}
                    >
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {typeIcons[transaction.type]}
                          <Badge
                            variant="outline"
                            className={typeColors[transaction.type]}
                          >
                            {transaction.type.replace("_", " ")}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {transaction.name || "Unnamed"}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">
                            {categoryIcons[transaction.category] || "📦"}
                          </span>
                          {transaction.category}
                        </div>
                      </TableCell>
                      <TableCell>
                        {transaction.eventDate
                          ? new Date(transaction.eventDate).toLocaleDateString()
                          : new Date(
                              transaction.createdAt || ""
                            ).toLocaleDateString()}
                      </TableCell>
                      <TableCell
                        className={`text-right font-medium ${
                          transaction.type === EXPENSE_TYPE.INCOME
                            ? "text-emerald-600"
                            : transaction.type === EXPENSE_TYPE.EXPENSE
                            ? "text-rose-600"
                            : ""
                        }`}
                      >
                        {transaction.type === EXPENSE_TYPE.INCOME
                          ? "+"
                          : transaction.type === EXPENSE_TYPE.EXPENSE
                          ? "-"
                          : ""}
                        ${Math.abs(transaction.amount).toFixed(2)}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger
                            asChild
                            onClick={(e) => e.stopPropagation()}
                          >
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontalIcon className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleViewDetails(transaction);
                              }}
                            >
                              <EyeIcon className="mr-2 h-4 w-4" />
                              View details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEdit(transaction);
                              }}
                            >
                              <PencilIcon className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(transaction._id || "");
                              }}
                            >
                              <TrashIcon className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </TableBody>
            </Table>
          </div>

          {/* Mobile view - Cards */}
          <div className="grid gap-3 md:hidden">
            <AnimatePresence>
              {paginatedTransactions.map((transaction, index) => (
                <motion.div
                  key={transaction._id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0 }}
                  transition={{ delay: index * 0.05, duration: 0.2 }}
                >
                  <div className="rounded-xl border bg-card shadow-sm overflow-hidden">
                    <div className="p-4 flex items-center justify-between border-b">
                      <div className="flex items-center gap-2">
                        {typeIcons[transaction.type]}
                        <Badge
                          variant="outline"
                          className={typeColors[transaction.type]}
                        >
                          {transaction.type.replace("_", " ")}
                        </Badge>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-8 w-8 p-0"
                          >
                            <MoreHorizontalIcon className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleViewDetails(transaction)}
                          >
                            <EyeIcon className="mr-2 h-4 w-4" />
                            View details
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleEdit(transaction)}
                          >
                            <PencilIcon className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive focus:text-destructive"
                            onClick={() => handleDelete(transaction._id || "")}
                          >
                            <TrashIcon className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <div
                      className="p-4"
                      onClick={() => handleViewDetails(transaction)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">
                          {transaction.name || "Unnamed"}
                        </h3>
                        <div
                          className={`font-medium ${
                            transaction.type === EXPENSE_TYPE.INCOME
                              ? "text-emerald-600"
                              : transaction.type === EXPENSE_TYPE.EXPENSE
                              ? "text-rose-600"
                              : ""
                          }`}
                        >
                          {transaction.type === EXPENSE_TYPE.INCOME
                            ? "+"
                            : transaction.type === EXPENSE_TYPE.EXPENSE
                            ? "-"
                            : ""}
                          ${Math.abs(transaction.amount).toFixed(2)}
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <span>
                            {categoryIcons[transaction.category] || "📦"}
                          </span>
                          <span>{transaction.category}</span>
                        </div>
                        <div>
                          {transaction.eventDate
                            ? new Date(
                                transaction.eventDate
                              ).toLocaleDateString()
                            : new Date(
                                transaction.createdAt || ""
                              ).toLocaleDateString()}
                        </div>
                      </div>
                      {transaction.note && (
                        <p className="mt-2 text-sm text-muted-foreground line-clamp-1">
                          {transaction.note}
                        </p>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </>
      )}

      {totalPages > 1 && (
        <div className="flex items-center justify-center mt-6">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() =>
                    setCurrentPage((prev) => Math.max(prev - 1, 1))
                  }
                  size="icon"
                  className={
                    currentPage === 1
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>

              {/* Mobile pagination - just show current/total */}
              <div className="md:hidden flex items-center">
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
              </div>

              {/* Desktop pagination - show page numbers */}
              <div className="hidden md:flex">
                {Array.from({ length: Math.min(totalPages, 5) }).map((_, i) => {
                  let pageNumber = i + 1;
                  if (totalPages > 5 && currentPage > 3) {
                    pageNumber = currentPage - 3 + i;
                    if (pageNumber > totalPages) {
                      return null;
                    }
                  }
                  return (
                    <PaginationItem key={i}>
                      <PaginationLink
                        size="icon"
                        onClick={() => setCurrentPage(pageNumber)}
                        isActive={currentPage === pageNumber}
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}
                {totalPages > 5 && currentPage < totalPages - 2 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}
                {totalPages > 5 && currentPage < totalPages - 1 && (
                  <PaginationItem>
                    <PaginationLink
                      size="icon"
                      onClick={() => setCurrentPage(totalPages)}
                    >
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                )}
              </div>

              <PaginationItem>
                <PaginationNext
                  size="icon"
                  onClick={() =>
                    setCurrentPage((prev) => Math.min(prev + 1, totalPages))
                  }
                  className={
                    currentPage === totalPages
                      ? "pointer-events-none opacity-50"
                      : "cursor-pointer"
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {selectedTransaction && (
        <>
          <TransactionDetailsSheet
            transaction={selectedTransaction}
            open={isDetailsOpen}
            onOpenChange={setIsDetailsOpen}
            onEdit={() => {
              setIsDetailsOpen(false);
              setIsEditOpen(true);
            }}
          />
          <EditTransactionSheet
            transaction={selectedTransaction}
            open={isEditOpen}
            onOpenChange={setIsEditOpen}
          />
        </>
      )}
    </div>
  );
}

function TransactionTableSkeleton() {
  return (
    <div className="space-y-4">
      {/* Desktop skeleton */}
      <div className="hidden md:block rounded-xl border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50 hover:bg-muted/50">
              <TableHead>Type</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Date</TableHead>
              <TableHead className="text-right">Amount</TableHead>
              <TableHead className="w-[80px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {Array.from({ length: 5 }).map((_, i) => (
              <TableRow key={i}>
                <TableCell>
                  <Skeleton className="h-8 w-20" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-32" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-24" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-5 w-28" />
                </TableCell>
                <TableCell className="text-right">
                  <Skeleton className="h-5 w-20 ml-auto" />
                </TableCell>
                <TableCell>
                  <Skeleton className="h-8 w-8 rounded-full" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile skeleton */}
      <div className="grid gap-3 md:hidden">
        {Array.from({ length: 5 }).map((_, i) => (
          <div
            key={i}
            className="rounded-xl border bg-card shadow-sm overflow-hidden"
          >
            <div className="p-4 flex items-center justify-between border-b">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-8 rounded-full" />
            </div>
            <div className="p-4">
              <div className="flex items-center justify-between mb-2">
                <Skeleton className="h-5 w-28" />
                <Skeleton className="h-5 w-20" />
              </div>
              <div className="flex items-center justify-between">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

function SearchIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <circle cx="11" cy="11" r="8" />
      <path d="m21 21-4.3-4.3" />
    </svg>
  );
}
