import { ExpensesByCategoryChart } from "@/components/expenses-by-category-chart";
import { RecentTransactions } from "@/components/recent-transactions";
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  CardDescription,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils";
import {
  EXPENSE_MAP,
  EXPENSE_TYPE,
  ExpenseStats,
} from "@/services/expense.service";
import { Bar<PERSON>hart, Loader2 } from "lucide-react";
import { useState, useEffect } from "react";
import {
  ResponsiveContainer,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Bar,
} from "recharts";

function Overview({
  expenseStats,
  loading,
}: {
  expenseStats?: ExpenseStats;
  loading?: boolean;
}) {
  return (
    <>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Object.keys(EXPENSE_MAP).map((key, index) => {
          return (
            <Card
              className="border-l-4 border-l-primary/70 shadow-sm"
              key={index}
            >
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {EXPENSE_MAP[key as EXPENSE_TYPE]}
                </CardTitle>
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                  {getIcon(key as EXPENSE_TYPE)}
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {loading ? (
                    <div className="flex items-center">
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      <span className="text-muted-foreground">Loading...</span>
                    </div>
                  ) : expenseStats ? (
                    formatCurrency(
                      expenseStats.groupedExpense[key as EXPENSE_TYPE]
                    )
                  ) : (
                    "$0.00"
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-semibold">Overview</CardTitle>
          </CardHeader>
          <CardContent>
            {expenseStats ? (
              <OverviewChart
                monthlyData={expenseStats.monthlyData}
                isLoading={loading}
              />
            ) : (
              <Overview isLoading={true} />
            )}
          </CardContent>
        </Card>
        <Card className="col-span-3 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-semibold">
              Expenses by Category
            </CardTitle>
            <CardDescription>
              Your spending distribution across categories
            </CardDescription>
          </CardHeader>
          <CardContent>
            {expenseStats ? (
              <ExpensesByCategoryChart
                categoryData={expenseStats.expensesByCategory}
                isLoading={loading}
              />
            ) : (
              <ExpensesByCategoryChart isLoading={true} />
            )}
          </CardContent>
        </Card>
      </div>
      <div className="grid gap-4 md:grid-cols-1">
        <Card className="col-span-1 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-semibold">
              Recent Transactions
            </CardTitle>
            <CardDescription>
              Your most recent financial activities
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {expenseStats ? (
              <RecentTransactions
                transactions={expenseStats.recentTransactions}
                isLoading={loading}
              />
            ) : (
              <RecentTransactions isLoading={true} />
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}

export default Overview;

// Fallback data in case API fails

interface OverviewProps {
  monthlyData?: Array<{
    name: string;
    income: number;
    expenses: number;
  }>;
  isLoading?: boolean;
}

export function OverviewChart({
  monthlyData,
  isLoading = false,
}: OverviewProps) {
  const [chartData, setChartData] = useState([]);
  const [loading, setLoading] = useState(isLoading);
  const [error, setError] = useState(false);

  // If data is provided via props, use it
  useEffect(() => {
    if (monthlyData && monthlyData.length > 0) {
      setChartData(monthlyData);
      setLoading(false);
      setError(false);
    } else if (monthlyData && monthlyData.length === 0) {
      setChartData([]);
      setLoading(false);
      setError(false);
    }
  }, [monthlyData]);

  if (loading) {
    return (
      <div className="w-full flex flex-col items-center justify-center py-10">
        <Skeleton className="h-[350px] w-full rounded-lg" />
        <div className="flex justify-center gap-6 mt-4">
          <Skeleton className="h-6 w-24" />
          <Skeleton className="h-6 w-24" />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full h-[350px] flex items-center justify-center border rounded-lg">
        <p className="text-muted-foreground">
          Failed to load chart data. Please try again later.
        </p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <ResponsiveContainer width="100%" height={350}>
        <BarChart
          data={chartData}
          margin={{ top: 10, right: 10, left: 0, bottom: 10 }}
        >
          <CartesianGrid
            strokeDasharray="3 3"
            vertical={false}
            stroke="#f0f0f0"
          />
          <XAxis
            dataKey="name"
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            padding={{ left: 10, right: 10 }}
          />
          <YAxis
            stroke="#888888"
            fontSize={12}
            tickLine={false}
            axisLine={false}
            tickFormatter={(value) => `${formatCurrency(value)}`}
            width={60}
          />
          <Tooltip
            formatter={(value: number) => [`${formatCurrency(value)}`, ""]}
            labelFormatter={(label) => `Month: ${label}`}
            contentStyle={{
              borderRadius: "8px",
              boxShadow: "0 4px 12px rgba(0,0,0,0.1)",
              border: "1px solid #e2e8f0",
              padding: "8px 12px",
            }}
            cursor={{ fill: "rgba(0, 0, 0, 0.04)" }}
          />
          <Bar
            dataKey="income"
            fill="hsl(142.1, 76.2%, 36.3%)"
            radius={[4, 4, 0, 0]}
            name="Income"
            barSize={24}
            animationDuration={1000}
          />
          <Bar
            dataKey="expenses"
            fill="hsl(346.8, 77.2%, 49.8%)"
            radius={[4, 4, 0, 0]}
            name="Expenses"
            barSize={24}
            animationDuration={1000}
          />
        </BarChart>
      </ResponsiveContainer>
      <div className="flex justify-center gap-6 mt-2">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-[hsl(142.1,76.2%,36.3%)]"></div>
          <span className="text-sm font-medium">Income</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 rounded-full bg-[hsl(346.8,77.2%,49.8%)]"></div>
          <span className="text-sm font-medium">Expenses</span>
        </div>
      </div>
    </div>
  );
}
