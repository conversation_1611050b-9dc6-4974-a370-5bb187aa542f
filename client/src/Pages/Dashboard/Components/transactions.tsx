"use client";

import { useState, useEffect } from "react";
import { CardContent } from "@/components/ui/card";
import { TransactionsTable } from "@/Pages/Dashboard/Components/table";
import { TransactionSummaryCards } from "@/Pages/Dashboard/Components/summary-cards";
import { TransactionFilters } from "@/components/transaction-filters";
import { motion } from "framer-motion";
import { useExpense } from "@/contexts/ExpenseContext";
import { useTransactions } from "@/hooks/useTransactions";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";

export default function Transactions() {
  const { dateRange, ignoreDate } = useExpense();

  const [filters, setFilters] = useState({
    type: "all",
    categories: [],
    dateRange: {
      from: dateRange.from,
      to: dateRange.to,
    },
    searchQuery: "",
    sortBy: "date",
    sortDirection: "desc" as "asc" | "desc",
    ignoreDate: ignoreDate,
  });

  // Fetch transactions using the custom hook
  const { transactions, isLoading } = useTransactions({ filters });

  // Update filters when dateRange or ignoreDate changes in context
  useEffect(() => {
    setFilters((prev) => ({
      ...prev,
      dateRange: {
        from: dateRange.from,
        to: dateRange.to,
      },
      ignoreDate: ignoreDate,
    }));
  }, [dateRange, ignoreDate]);

  return (
    <div className="flex-1 space-y-6 p-4 md:p-1 pt-2">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="rounded-xl overflow-hidden border shadow-sm bg-card"
      >
        <div className="p-6 bg-muted/30 border-b">
          <div className="flex flex-col gap-2">
            <h2 className="text-2xl font-semibold">All Transactions</h2>
            <p className="text-muted-foreground">
              View, search, and manage all your financial activities
            </p>
            {/* Date range indicator */}
            <div className="bg-muted/50 rounded-md p-2 mt-2 text-sm text-muted-foreground flex items-center">
              <CalendarIcon className="h-4 w-4 mr-2" />
              {ignoreDate ? (
                <span>
                  <span className="font-medium">Showing all transactions</span>{" "}
                  (date filter disabled)
                </span>
              ) : dateRange.from && dateRange.to ? (
                <span>
                  Showing transactions from{" "}
                  <span className="font-medium">
                    {format(dateRange.from, "MMMM d, yyyy")}
                  </span>{" "}
                  to{" "}
                  <span className="font-medium">
                    {format(dateRange.to, "MMMM d, yyyy")}
                  </span>
                </span>
              ) : (
                <span>No date range selected</span>
              )}
            </div>
          </div>
        </div>
        <CardContent className="p-6 space-y-6">
          <TransactionFilters
            filters={filters}
            setFilters={setFilters}
            hideDateFilter={true}
          />
          <TransactionSummaryCards
            transactions={transactions}
            isLoading={isLoading}
          />
          <TransactionsTable
            filters={filters}
            transactions={transactions}
            isLoading={isLoading}
          />
        </CardContent>
      </motion.div>

      {/* <AddTransactionSheet
        open={isAddTransactionOpen}
        onOpenChange={setIsAddTransactionOpen}
        onSuccess={refreshData}
      /> */}

      {/* Mobile floating action button */}
    </div>
  );
}
