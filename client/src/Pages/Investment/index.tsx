"use client";

import { useState, useEffect, useCallback } from "react";
import {
  BarChart3Icon,
  CoinsIcon,
  LineChartIcon,
  PlusIcon,
  TrendingUpIcon,
  Loader2,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { InvestmentTypeChart } from "@/components/investment-type-chart";
import { InvestmentPerformanceChart } from "@/components/investment-performance-chart";
import { InvestmentPortfolio } from "@/components/investment-portfolio";
import { AddInvestmentSheet } from "@/components/add-investment-sheet";
import {
  InvestmentProvider,
  useInvestment,
} from "@/contexts/InvestmentContext";
import { formatCurrency } from "@/lib/utils";
import {
  Investment,
  INVESTMENT_TYPE,
  INVESTMENT_PURPOSE,
  StockDataType,
} from "@/services/investment.service";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteInvestment } from "@/services/investment.service";
import { toast } from "@/components/ui/use-toast";
import StocksChart from "@/components/StocksChart";
import { getPriceTracking } from "@/services/investment.service";

function InvestmentContent() {
  const [isAddInvestmentOpen, setIsAddInvestmentOpen] = useState(false);
  const [selectedInvestment, setSelectedInvestment] =
    useState<Investment | null>(null);

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // New state variables for categorized data
  const [stocksData, setStocksData] = useState<StockDataType[]>([]);
  const [ownedStocksData, setOwnedStocksData] = useState<StockDataType[]>([]);
  const [monitoredStocksData, setMonitoredStocksData] = useState<
    StockDataType[]
  >([]);
  const [mutualFundsData, setMutualFundsData] = useState<StockDataType[]>([]);
  const [commoditiesData, setCommoditiesData] = useState<StockDataType[]>([]);
  const [stocksFilter, setStocksFilter] = useState<
    "all" | "owned" | "monitored"
  >("all");

  const [isLoadingStocks, setIsLoadingStocks] = useState(false);

  const { investmentStats, loading, error, refreshData } = useInvestment();

  const handleEditInvestment = (investment: Investment) => {
    setSelectedInvestment(investment);
    setIsAddInvestmentOpen(true);
  };

  const handleDeleteInvestment = (investment: Investment) => {
    setSelectedInvestment(investment);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!selectedInvestment?._id) return;

    try {
      await deleteInvestment(selectedInvestment._id);
      toast({
        title: "Investment deleted",
        description: "Your investment has been deleted successfully.",
      });
      refreshData();
    } catch (error) {
      console.error("Failed to delete investment:", error);
      toast({
        title: "Error",
        description: "Failed to delete investment. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setSelectedInvestment(null);
    }
  };

  const fetchStockData = useCallback(async () => {
    try {
      setIsLoadingStocks(true);
      Promise.all(
        Object.keys(INVESTMENT_TYPE).map((type) =>
          getPriceTracking(type.toLowerCase())
            .then((data) => ({ type, data }))
            .catch((error) => {
              return { type, data: [], error };
            })
        )
      ).then((results) => {
        const tStocksData = [];
        const tOwnedStocksData = [];
        const tMonitoredStocksData = [];
        const tMutualFundsData = [];
        const tCommoditiesData = [];

        // Get the list of owned and monitored investments from investmentStats
        const ownedSymbols =
          investmentStats?.recentInvestments
            .filter(
              (inv) =>
                inv.purpose === INVESTMENT_PURPOSE.OWNED &&
                inv.type === INVESTMENT_TYPE.STOCK
            )
            .map((inv) => inv.symbol.toUpperCase()) || [];

        const monitoredSymbols =
          investmentStats?.recentInvestments
            .filter(
              (inv) =>
                inv.purpose === INVESTMENT_PURPOSE.MONITORING &&
                inv.type === INVESTMENT_TYPE.STOCK
            )
            .map((inv) => inv.symbol.toUpperCase()) || [];

        results.forEach(({ type, data }) => {
          if (type === INVESTMENT_TYPE.STOCK) {
            // Add purpose to each stock based on the symbol
            const stocksWithPurpose = data.map((stock: StockDataType) => {
              const stockSymbol = stock.name.split(" ")[0].toUpperCase();
              let purpose: INVESTMENT_PURPOSE | undefined;

              if (ownedSymbols.includes(stockSymbol)) {
                purpose = INVESTMENT_PURPOSE.OWNED;
              } else if (monitoredSymbols.includes(stockSymbol)) {
                purpose = INVESTMENT_PURPOSE.MONITORING;
              }

              return { ...stock, purpose };
            });

            tStocksData.push(...stocksWithPurpose);

            // Separate owned and monitored stocks
            stocksWithPurpose.forEach((stock) => {
              if (stock.purpose === INVESTMENT_PURPOSE.OWNED) {
                tOwnedStocksData.push(stock);
              } else if (stock.purpose === INVESTMENT_PURPOSE.MONITORING) {
                tMonitoredStocksData.push(stock);
              }
            });
          } else if (type === INVESTMENT_TYPE.MUTUAL_FUND) {
            tMutualFundsData.push(...data);
          } else {
            // Gold, Silver, Currency
            tCommoditiesData.push(...data);
          }
        });

        setStocksData(tStocksData);
        setOwnedStocksData(tOwnedStocksData);
        setMonitoredStocksData(tMonitoredStocksData);
        setMutualFundsData(tMutualFundsData);
        setCommoditiesData(tCommoditiesData);
      });
    } catch (error) {
      console.error("Failed to fetch stock data:", error);
    } finally {
      setIsLoadingStocks(false);
    }
  }, [investmentStats]);

  // Fetch stock data on initial load and when investmentStats changes
  useEffect(() => {
    fetchStockData();
  }, [fetchStockData]);

  if (loading) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
        <p className="text-lg text-muted-foreground">
          Loading your investment data...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <div className="text-center max-w-md p-6 border rounded-lg shadow-sm">
          <h2 className="text-2xl font-bold text-rose-500 mb-2">
            Error Loading Data
          </h2>
          <p className="text-muted-foreground mb-4">
            We couldn't load your investment data. Please try refreshing the
            page.
          </p>
          <Button onClick={() => refreshData()}>Retry</Button>
        </div>
      </div>
    );
  }
  console.log({ stocksData, mutualFundsData, commoditiesData });

  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Investments</h2>
          <div className="flex items-center space-x-2 hidden md:block">
            <Button onClick={() => setIsAddInvestmentOpen(true)}>
              <PlusIcon className="mr-2 h-4 w-4" />
              Add Investment
            </Button>
          </div>
        </div>
        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="w-full md:w-auto">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="portfolio">Portfolio</TabsTrigger>
            <TabsTrigger value="market">Market Data</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="border-l-4 border-l-primary/70 shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Investment
                  </CardTitle>
                  <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <CoinsIcon className="h-4 w-4 text-primary" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {investmentStats
                      ? formatCurrency(investmentStats.totalInvestment)
                      : "$0.00"}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-emerald-500/70 shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Current Value
                  </CardTitle>
                  <div className="h-8 w-8 rounded-full bg-emerald-500/10 flex items-center justify-center">
                    <BarChart3Icon className="h-4 w-4 text-emerald-500" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {investmentStats
                      ? formatCurrency(investmentStats.totalCurrentValue)
                      : "$0.00"}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-blue-500/70 shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Return
                  </CardTitle>
                  <div className="h-8 w-8 rounded-full bg-blue-500/10 flex items-center justify-center">
                    <LineChartIcon className="h-4 w-4 text-blue-500" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {investmentStats
                      ? formatCurrency(investmentStats.totalReturn)
                      : "$0.00"}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-l-4 border-l-amber-500/70 shadow-sm">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Return %
                  </CardTitle>
                  <div className="h-8 w-8 rounded-full bg-amber-500/10 flex items-center justify-center">
                    <TrendingUpIcon className="h-4 w-4 text-amber-500" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {investmentStats
                      ? `${investmentStats.returnPercentage.toFixed(2)}%`
                      : "0.00%"}
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              <Card className="col-span-4 shadow-sm">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg font-semibold">
                    Performance
                  </CardTitle>
                  <CardDescription>
                    Your investment performance over time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {investmentStats && (
                    <InvestmentPerformanceChart
                      performanceData={investmentStats.performanceData}
                      isLoading={false}
                    />
                  )}
                </CardContent>
              </Card>

              <Card className="col-span-3 shadow-sm">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg font-semibold">
                    Investment by Type
                  </CardTitle>
                  <CardDescription>
                    Your investment distribution across types
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {investmentStats && (
                    <InvestmentTypeChart
                      typeData={investmentStats.investmentsByType}
                      isLoading={false}
                    />
                  )}
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-4 md:grid-cols-1">
              <Card className="col-span-1 shadow-sm">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg font-semibold">
                    Recent Investments
                  </CardTitle>
                  <CardDescription>
                    Your most recent investment activities
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {investmentStats && (
                    <InvestmentPortfolio
                      investments={investmentStats.recentInvestments.slice(
                        0,
                        5
                      )}
                      isLoading={false}
                      onEdit={handleEditInvestment}
                      onDelete={handleDeleteInvestment}
                    />
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="portfolio" className="space-y-4">
            <div className="h-full">
              {investmentStats && (
                <Card className="shadow-sm">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-lg font-semibold">
                      Your Investment Portfolio
                    </CardTitle>
                    <CardDescription>
                      Manage all your investments in one place
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <InvestmentPortfolio
                      investments={investmentStats.recentInvestments}
                      isLoading={false}
                      onEdit={handleEditInvestment}
                      onDelete={handleDeleteInvestment}
                    />
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          <TabsContent value="market" className="space-y-4">
            <div className="h-full">
              <div className="mb-4">
                <h2 className="text-lg font-semibold">Market Data</h2>
                <p className="text-sm text-muted-foreground">
                  Track the performance of your investments in the market
                </p>
              </div>

              {isLoadingStocks ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : !isLoadingStocks ? (
                <Tabs defaultValue="commodities" className="mt-6">
                  <TabsList className="mb-4">
                    <TabsTrigger value="commodities">
                      Gold/Silver/Currency
                    </TabsTrigger>
                    <TabsTrigger value="stocks">Stocks</TabsTrigger>
                    <TabsTrigger value="mutual-funds">Mutual Funds</TabsTrigger>
                  </TabsList>

                  <TabsContent value="commodities" className="mt-0">
                    {commoditiesData.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                        {commoditiesData.map((item) => (
                          <StocksChart folio={[item]} key={item.name} />
                        ))}
                      </div>
                    ) : (
                      <div className="flex justify-center items-center h-40 text-muted-foreground">
                        No commodities data available
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="stocks" className="mt-0">
                    <div className="mb-4 flex items-center gap-2">
                      <span className="text-sm font-medium">Filter:</span>
                      <div className="flex gap-2">
                        <Button
                          variant={
                            stocksFilter === "all" ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => setStocksFilter("all")}
                        >
                          All
                        </Button>
                        <Button
                          variant={
                            stocksFilter === "owned" ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => setStocksFilter("owned")}
                        >
                          Owned
                        </Button>
                        <Button
                          variant={
                            stocksFilter === "monitored" ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() => setStocksFilter("monitored")}
                        >
                          Monitoring
                        </Button>
                      </div>
                    </div>

                    {stocksData.length > 0 ? (
                      <>
                        {stocksFilter === "all" && (
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                            {stocksData.map((item) => (
                              <StocksChart folio={[item]} key={item.name} />
                            ))}
                          </div>
                        )}

                        {stocksFilter === "owned" && (
                          <>
                            {ownedStocksData.length > 0 ? (
                              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                                {ownedStocksData.map((item) => (
                                  <StocksChart folio={[item]} key={item.name} />
                                ))}
                              </div>
                            ) : (
                              <div className="flex justify-center items-center h-40 text-muted-foreground">
                                No owned stocks data available
                              </div>
                            )}
                          </>
                        )}

                        {stocksFilter === "monitored" && (
                          <>
                            {monitoredStocksData.length > 0 ? (
                              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                                {monitoredStocksData.map((item) => (
                                  <StocksChart folio={[item]} key={item.name} />
                                ))}
                              </div>
                            ) : (
                              <div className="flex justify-center items-center h-40 text-muted-foreground">
                                No monitored stocks data available
                              </div>
                            )}
                          </>
                        )}
                      </>
                    ) : (
                      <div className="flex justify-center items-center h-40 text-muted-foreground">
                        No stocks data available
                      </div>
                    )}
                  </TabsContent>

                  <TabsContent value="mutual-funds" className="mt-0">
                    {mutualFundsData.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
                        {mutualFundsData.map((item) => (
                          <StocksChart folio={[item]} key={item.name} />
                        ))}
                      </div>
                    ) : (
                      <div className="flex justify-center items-center h-40 text-muted-foreground">
                        No mutual funds data available
                      </div>
                    )}
                  </TabsContent>
                </Tabs>
              ) : (
                <div className="flex justify-center items-center h-64 text-muted-foreground">
                  No market data available
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      <AddInvestmentSheet
        open={isAddInvestmentOpen}
        onOpenChange={(open) => {
          setIsAddInvestmentOpen(open);
          // Reset selectedInvestment when closing the sheet
          if (!open) {
            setSelectedInvestment(null);
          }
        }}
        onSuccess={refreshData}
        selectedInvestment={selectedInvestment}
      />

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the investment "
              {selectedInvestment?.name}". This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-destructive text-destructive-foreground"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <div className="fixed right-4 bottom-4 md:hidden z-10">
        <Button
          onClick={() => setIsAddInvestmentOpen(true)}
          size="icon"
          className="h-14 w-14 rounded-full shadow-lg"
        >
          <PlusIcon className="h-6 w-6" />
          <span className="sr-only">Add Investment</span>
        </Button>
      </div>
    </div>
  );
}

export default function InvestmentPage() {
  return (
    <InvestmentProvider>
      <InvestmentContent />
    </InvestmentProvider>
  );
}
